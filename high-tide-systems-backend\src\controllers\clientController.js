// src/controllers/clientController.js
const bcrypt = require('bcryptjs');
const { validationResult } = require('express-validator');
const { body } = require('express-validator');
const prisma = require('../utils/prisma');

// Validações
const createClientValidation = [
  body('login').notEmpty().withMessage('Login é obrigatório'),
  body('email').isEmail().withMessage('Email inválido'),
  body('password').isLength({ min: 6 }).withMessage('Senha deve ter no mínimo 6 caracteres'),
  // Person data validations
  body('person.fullName').notEmpty().withMessage('Nome completo é obrigatório'),
  body('person.cpf').optional().matches(/^\d{11}$/).withMessage('CPF inválido'),
  body('person.phone').optional(),
  body('person.email').optional().isEmail().withMessage('Email inválido'),
];

class ClientController {
  /**
   * Cria um novo cliente no sistema com pessoa associada
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        login,
        email,
        password,
        person
      } = req.body;

      // Verificar se cliente já existe (email, login)
      const clientExists = await prisma.client.findFirst({
        where: {
          OR: [
            { email },
            { login },
          ],
        },
      });

      if (clientExists) {
        if (clientExists.email === email) {
          return res.status(400).json({ message: 'Email já cadastrado' });
        }
        if (clientExists.login === login) {
          return res.status(400).json({ message: 'Login já cadastrado' });
        }
      }

      // Verificar se CPF já existe
      if (person.cpf) {
        const personExists = await prisma.person.findUnique({
          where: { cpf: person.cpf }
        });

        if (personExists) {
          return res.status(400).json({ message: 'CPF já cadastrado' });
        }
      }

      const hashedPassword = await bcrypt.hash(password, 10);

      // Criação de cliente e pessoa em transação
      const result = await prisma.$transaction(async (prisma) => {
        // Criar cliente
        const client = await prisma.client.create({
          data: {
            login,
            email,
            password: hashedPassword,
            createdById: req.user.id,
          },
        });

        // Criar pessoa associada ao cliente
        const personData = await prisma.person.create({
          data: {
            fullName: person.fullName,
            cpf: person.cpf,
            birthDate: person.birthDate ? new Date(person.birthDate) : null,
            address: person.address,
            neighborhood: person.neighborhood,
            city: person.city,
            state: person.state,
            postalCode: person.postalCode,
            phone: person.phone,
            email: person.email || email, // Use client email by default
            gender: person.gender,
            notes: person.notes,
            relationship: 'Titular', // Default relationship
            createdById: req.user.id,
          }
        });

        // Criar relacionamento ClientPerson
        await prisma.clientPerson.create({
          data: {
            clientId: client.id,
            personId: personData.id,
            relationship: 'Titular',
            isPrimary: true,
          }
        });

        return { client, personData };
      });

      res.status(201).json({
        client: result.client,
        person: result.personData
      });
    } catch (error) {
      console.error('Erro ao criar cliente:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Atualiza dados de um cliente existente
   */
  static async update(req, res) {
    try {
      const { id } = req.params;
      const { email, password } = req.body;

      let updateData = { email };

      if (password) {
        updateData.password = await bcrypt.hash(password, 10);
      }

      const client = await prisma.client.update({
        where: { id },
        data: updateData,
        select: {
          id: true,
          login: true,
          email: true,
          active: true,
          updatedAt: true,
        },
      });

      res.json(client);
    } catch (error) {
      console.error('Erro ao atualizar cliente:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Busca o perfil completo de um cliente
   */
  static async getProfile(req, res) {
    try {
      const { id } = req.params;

      const client = await prisma.client.findUnique({
        where: { id },
        select: {
          id: true,
          login: true,
          email: true,
          active: true,
          createdAt: true,
          persons: {
            where: {
              deletedAt: null,
              active: true
            },
            include: {
              personInsurances: {
                include: {
                  insurance: true
                }
              }
            }
          }
        },
      });

      if (!client) {
        return res.status(404).json({ message: 'Cliente não encontrado' });
      }

      res.json(client);
    } catch (error) {
      console.error('Erro ao buscar perfil:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Lista clientes do sistema com paginação e filtros
   */
  static async list(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        active,
        companyId,
        clientIds,
        include_persons,
        sortField = 'fullName', // Default sort by fullName
        sortDirection = 'asc'   // Default sort direction
      } = req.query;

      // Processar clientIds se existir
      let clientIdsArray = [];
      if (clientIds) {
        // Se for um único valor, converter para array
        if (!Array.isArray(clientIds)) {
          clientIdsArray = [clientIds];
        } else {
          clientIdsArray = clientIds;
        }
        console.log("Filtrando por IDs de clientes:", clientIdsArray);
      }

      const where = {
        AND: [
          search
            ? {
                OR: [
                  { email: { contains: search, mode: 'insensitive' } },
                  { login: { contains: search, mode: 'insensitive' } },
                  {
                    clientPersons: {
                      some: {
                        person: {
                          OR: [
                            { fullName: { contains: search, mode: 'insensitive' } },
                            { cpf: { contains: search } },
                          ]
                        }
                      }
                    }
                  }
                ],
              }
            : {},
          // Filtrar por IDs específicos se fornecidos
          clientIdsArray.length > 0
            ? { id: { in: clientIdsArray } }
            : {},
          active !== undefined ? { active: active === 'true' } : {},
          // Filtrar por empresa se o parâmetro for fornecido e o usuário for SYSTEM_ADMIN
          companyId && req.user.role === 'SYSTEM_ADMIN' ? { companyId } : {},
          // Se não for SYSTEM_ADMIN e não for cliente, limitar à empresa do usuário
          !req.user.isClient && req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId ? { companyId: req.user.companyId } : {},
        ],
      };

      // Determinar a ordenação com base nos parâmetros
      let orderBy = [];

      // Se o campo de ordenação for fullName, precisamos ordenar pela relação com persons
      if (sortField === 'fullName') {
        // Primeiro, buscar todos os clientes com seus titulares para ordenação global
        const allClients = await prisma.client.findMany({
          where,
          select: {
            id: true,
            login: true,
            email: true,
            active: true,
            createdAt: true,
            persons: {
              where: {
                relationship: 'Titular',
                deletedAt: null
              },
              select: {
                id: true,
                fullName: true
              },
              take: 1,
              orderBy: { fullName: sortDirection.toLowerCase() }
            }
          },
        });

        // Ordenar todos os clientes pelo nome do titular
        const sortedClients = allClients.sort((a, b) => {
          const nameA = a.persons && a.persons[0] && a.persons[0].fullName
            ? a.persons[0].fullName.toLowerCase()
            : a.login.toLowerCase();

          const nameB = b.persons && b.persons[0] && b.persons[0].fullName
            ? b.persons[0].fullName.toLowerCase()
            : b.login.toLowerCase();

          return sortDirection.toLowerCase() === 'asc'
            ? nameA.localeCompare(nameB)
            : nameB.localeCompare(nameA);
        });

        // Extrair os IDs na ordem correta
        const sortedIds = sortedClients.map(client => client.id);

        // Aplicar paginação aos IDs ordenados
        const paginatedIds = sortedIds.slice((page - 1) * limit, page * limit);

        // Buscar os clientes completos usando os IDs paginados
        const clients = await prisma.client.findMany({
          where: {
            id: { in: paginatedIds }
          },
          select: {
            id: true,
            login: true,
            email: true,
            active: true,
            createdAt: true,
            clientPersons: {
              where: include_persons === 'true'
                ? { person: { deletedAt: null } } // Incluir todas as pessoas se include_persons for true
                : {
                    person: { deletedAt: null },
                    relationship: 'Titular'
                  }, // Caso contrário, apenas o titular
              include: {
                person: {
                  select: {
                    id: true,
                    fullName: true,
                    cpf: true,
                    phone: true,
                    email: true,
                    gender: true,
                    address: true,
                    neighborhood: true,
                    city: true,
                    state: true,
                    postalCode: true,
                    birthDate: true,
                    relationship: true
                  }
                }
              },
              // Não limitar a quantidade se include_persons for true
              ...(include_persons !== 'true' && { take: 1 }),
              // Ordenar pessoas pelo nome completo
              orderBy: { person: { fullName: 'asc' } }
            }
          },
        });

        // Reordenar os resultados para manter a ordem dos IDs
        const orderedClients = paginatedIds.map(id =>
          clients.find(client => client.id === id)
        ).filter(Boolean);

        const total = await prisma.client.count({ where });

        res.json({
          clients: orderedClients,
          total,
          pages: Math.ceil(total / limit),
        });
      } else {
        // Para outros campos, podemos usar a ordenação direta do Prisma
        if (sortField === 'login' || sortField === 'email' || sortField === 'createdAt') {
          orderBy.push({ [sortField]: sortDirection.toLowerCase() });
        } else {
          // Default para login se o campo não for reconhecido
          orderBy.push({ login: 'asc' });
        }

        const [clients, total] = await Promise.all([
          prisma.client.findMany({
            where,
            skip: (page - 1) * limit,
            take: Number(limit),
            orderBy,
            select: {
              id: true,
              login: true,
              email: true,
              active: true,
              createdAt: true,
              clientPersons: {
                where: include_persons === 'true'
                  ? { person: { deletedAt: null } } // Incluir todas as pessoas se include_persons for true
                  : {
                      person: { deletedAt: null },
                      relationship: 'Titular'
                    }, // Caso contrário, apenas o titular
                include: {
                  person: {
                    select: {
                      id: true,
                      fullName: true,
                      cpf: true,
                      phone: true,
                      email: true,
                      gender: true,
                      address: true,
                      neighborhood: true,
                      city: true,
                      state: true,
                      postalCode: true,
                      birthDate: true,
                      relationship: true
                    }
                  }
                },
                // Não limitar a quantidade se include_persons for true
                ...(include_persons !== 'true' && { take: 1 }),
                // Ordenar pessoas pelo nome completo
                orderBy: { person: { fullName: 'asc' } }
              }
            },
          }),
          prisma.client.count({ where }),
        ]);

        res.json({
          clients,
          total,
          pages: Math.ceil(total / limit),
        });
      }
    } catch (error) {
      console.error('Erro ao listar clientes:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Alterna o status de ativo/inativo de um cliente
   */
  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      const client = await prisma.client.findUnique({ where: { id } });
      if (!client) {
        return res.status(404).json({ message: 'Cliente não encontrado' });
      }

      const updatedClient = await prisma.client.update({
        where: { id },
        data: { active: !client.active },
        select: {
          id: true,
          login: true,
          email: true,
          active: true,
        },
      });

      res.json(updatedClient);
    } catch (error) {
      console.error('Erro ao alterar status do cliente:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Remove permanentemente um cliente do sistema e todos os seus dados relacionados (hard delete)
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o cliente existe e obter todas as relações importantes
      const client = await prisma.client.findUnique({
        where: { id },
        include: {
          ClientInsurance: true,
          Document: true,
          Recurrence: {
            include: {
              patterns: true
            }
          },
          Scheduling: true,
          clientPersons: {
            include: {
              person: {
                include: {
                  personInsurances: true,
                  contacts: true,
                  documents: true,
                  insuranceServiceLimits: true,
                  schedulings: true
                }
              }
            }
          }
        }
      });

      if (!client) {
        return res.status(404).json({ message: 'Cliente não encontrado' });
      }

      // Verificar se há agendamentos ativos
      const activeAppointments = client.Scheduling.filter(
        scheduling => !['CANCELLED', 'COMPLETED'].includes(scheduling.status)
      );

      console.log('Agendamentos ativos encontrados:', activeAppointments.length);

      if (activeAppointments.length > 0) {
        return res.status(400).json({
          message: 'Não é possível excluir um cliente com agendamentos ativos'
        });
      }

      // Extrair IDs das pessoas associadas ao cliente
      const personIds = client.clientPersons.map(cp => cp.person.id);
      console.log(`Pessoas associadas ao cliente ${id}:`, personIds);

      // Iniciar uma transação para garantir que todos os registros relacionados sejam excluídos
      await prisma.$transaction(async (tx) => {
        // 1. Excluir ClientInsurance
        await tx.clientInsurance.deleteMany({
          where: { clientId: id }
        });

        // 2. Para cada recorrência do cliente
        for (const recurrence of client.Recurrence) {
          // Excluir padrões de recorrência
          await tx.recurrencePattern.deleteMany({
            where: { recurrenceId: recurrence.id }
          });

          // Excluir a recorrência
          await tx.recurrence.delete({
            where: { id: recurrence.id }
          });
        }

        // 3. Para cada agendamento do cliente
        for (const scheduling of client.Scheduling) {
          // Remover relações many-to-many com pessoas
          await tx.$executeRaw`DELETE FROM "_PersonToScheduling" WHERE "B" = ${scheduling.id}`;

          // Excluir o agendamento
          await tx.scheduling.delete({
            where: { id: scheduling.id }
          });
        }

        // 4. Para cada pessoa associada ao cliente
        for (const clientPerson of client.clientPersons) {
          const person = clientPerson.person;

          // Excluir limites de serviço de convênio
          await tx.personInsuranceServiceLimit.deleteMany({
            where: { personId: person.id }
          });

          // Excluir convênios relacionados à pessoa
          await tx.personInsurance.deleteMany({
            where: { personId: person.id }
          });

          // Excluir contatos relacionados à pessoa
          await tx.contact.deleteMany({
            where: { personId: person.id }
          });

          // Excluir documentos relacionados à pessoa
          await tx.document.deleteMany({
            where: { personId: person.id }
          });

          // Remover a pessoa dos agendamentos (relação many-to-many)
          await tx.$executeRaw`DELETE FROM "_PersonToScheduling" WHERE "A" = ${person.id}`;

          // Excluir relacionamento ClientPerson
          await tx.clientPerson.deleteMany({
            where: { personId: person.id }
          });

          // Excluir a pessoa
          await tx.person.delete({
            where: { id: person.id }
          });
        }

        // 5. Excluir documentos associados diretamente ao cliente
        await tx.document.deleteMany({
          where: { clientId: id }
        });

        // 6. Finalmente, excluir o cliente
        await tx.client.delete({
          where: { id }
        });
      });

      console.log(`Cliente ${id} e ${personIds.length} pessoas associadas excluídos permanentemente`);

      res.status(204).send();
    } catch (error) {
      console.error('Erro ao deletar cliente:', error);
      console.error('Detalhes do erro:', error.message);
      if (error.meta) console.error('Meta:', error.meta);

      res.status(500).json({
        message: 'Erro interno do servidor',
        error: error.message,
        meta: error.meta
      });
    }
  }
}

module.exports = {
  ClientController,
  createClientValidation,
};