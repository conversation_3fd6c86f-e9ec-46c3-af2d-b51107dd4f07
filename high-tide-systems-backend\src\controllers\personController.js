// src/controllers/personController.js
const { validationResult } = require('express-validator');
const { body } = require('express-validator');
const prisma = require('../utils/prisma');
const path = require('path');
const fs = require('fs').promises;
const { formatSuccessResponse, formatErrorResponse } = require('../utils/responseUtil');

// Caminho base para uploads
const UPLOAD_PATH = process.env.NODE_ENV === 'production'
  ? path.resolve("/tmp/uploads")
  : path.resolve("./uploads");

console.log('Caminho base para uploads (controller):', UPLOAD_PATH);

// Garante que o diretório de uploads existe
const ensureUploadDirectoryExists = async () => {
  try {
    await fs.access(UPLOAD_PATH);
  } catch (error) {
    await fs.mkdir(UPLOAD_PATH, { recursive: true });
  }

  // Garantir que o diretório de imagens de perfil existe
  const profileImagesPath = path.join(UPLOAD_PATH, "profile-images");
  try {
    await fs.access(profileImagesPath);
  } catch (error) {
    await fs.mkdir(profileImagesPath, { recursive: true });
  }
};

// Chamar a função para garantir que os diretórios existam
ensureUploadDirectoryExists().catch(err => {
  console.error('Erro ao criar diretórios de upload:', err);
});

// Validation rules
const createPersonValidation = [
  body('fullName').notEmpty().withMessage('Nome completo é obrigatório'),
  body('cpf')
    .optional()
    .custom((value) => {
      if (value && !/^\d{11}$/.test(value)) {
        throw new Error('CPF inválido. Digite apenas números (11 dígitos)');
      }
      return true;
    }),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Email inválido'),
  body('phone')
    .optional()
    .matches(/^\d{10,11}$/)
    .withMessage('Telefone inválido. Digite apenas números (10 ou 11 dígitos)'),
  body('clientId')
    .optional()
    .isUUID()
    .withMessage('ID do cliente inválido'),
  body('gender')
    .optional()
    .isIn(['M', 'F', 'O', ''])
    .withMessage('Gênero inválido'),
];

class PersonController {
  /**
   * Upload profile image for a person
   */
  static async uploadProfileImage(req, res) {
    try {
      console.log('Upload de imagem de perfil iniciado');
      console.log('Headers:', req.headers);
      console.log('Params:', req.params);
      console.log('User:', req.user?.id);

      const { id } = req.params;

      // Verificar se a pessoa existe
      const person = await prisma.person.findUnique({
        where: { id }
      });

      if (!person) {
        console.log(`Pessoa com ID ${id} não encontrada`);
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      console.log('Pessoa encontrada:', person.fullName);

      // Verificar se um arquivo foi enviado
      if (!req.file) {
        console.log('Nenhum arquivo enviado');
        return res.status(400).json({ message: 'Nenhuma imagem enviada' });
      }

      console.log('Arquivo recebido:', req.file);

      // Caminho relativo do arquivo para armazenar no banco de dados
      const relativePath = path.relative(UPLOAD_PATH, req.file.path);
      console.log('Caminho relativo do arquivo:', relativePath);
      console.log('Caminho completo do arquivo:', req.file.path);

      // Se a pessoa já tinha uma imagem de perfil, excluir a antiga
      if (person.profileImageUrl) {
        try {
          console.log('Pessoa já possui imagem de perfil:', person.profileImageUrl);
          const oldImagePath = path.join(UPLOAD_PATH, person.profileImageUrl);
          console.log('Caminho completo da imagem antiga:', oldImagePath);

          await fs.access(oldImagePath); // Verifica se o arquivo existe
          console.log('Arquivo antigo encontrado, excluindo...');
          await fs.unlink(oldImagePath); // Exclui o arquivo
          console.log('Arquivo antigo excluído com sucesso');
        } catch (error) {
          console.log('Arquivo antigo não encontrado ou erro ao excluir:', error);
          // Continua mesmo se não conseguir excluir o arquivo antigo
        }
      } else {
        console.log('Pessoa não possui imagem de perfil anterior');
      }

      // Atualizar a pessoa com a nova URL da imagem
      console.log('Atualizando pessoa com a nova URL da imagem:', relativePath);
      const updatedPerson = await prisma.person.update({
        where: { id },
        data: {
          profileImageUrl: relativePath
        }
      });
      console.log('Pessoa atualizada com sucesso:', updatedPerson.id);

      try {
        // Log this action
        console.log('Registrando ação no log de auditoria');
        await prisma.auditLog.create({
          data: {
            userId: req.user?.id || 'system',
            action: 'UPDATE',
            entityType: 'Person',
            entityId: id,
            details: { action: 'Upload profile image' },
            ipAddress: req.ip || req.connection.remoteAddress,
            userAgent: req.headers['user-agent'],
            companyId: req.user?.companyId
          }
        });
        console.log('Ação registrada com sucesso no log de auditoria');
      } catch (error) {
        console.error('Erro ao registrar ação no log de auditoria:', error);
        // Continua mesmo se não conseguir registrar o log
      }

      console.log('Enviando resposta de sucesso');

      // Construir a URL completa para a imagem
      const baseUrl = `${req.protocol}://${req.get('host')}`;
      const fullImageUrl = `${baseUrl}/uploads/${relativePath}`;
      console.log('URL completa da imagem:', fullImageUrl);

      res.status(200).json({
        message: 'Imagem de perfil atualizada com sucesso',
        profileImageUrl: relativePath,
        fullImageUrl: fullImageUrl,
        filename: path.basename(req.file.path)
      });
    } catch (error) {
      console.error('Erro ao fazer upload de imagem de perfil:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Get profile image for a person
   */
  static async getProfileImage(req, res) {
    try {
      console.log('Obtendo imagem de perfil');
      console.log('Params:', req.params);

      const { id } = req.params;

      // Verificar se a pessoa existe
      console.log(`Buscando pessoa com ID ${id}`);
      const person = await prisma.person.findUnique({
        where: { id }
      });

      if (!person) {
        console.log(`Pessoa com ID ${id} não encontrada`);
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }
      console.log('Pessoa encontrada:', person.fullName);

      // Verificar se a pessoa tem uma imagem de perfil
      if (!person.profileImageUrl) {
        console.log('Pessoa não possui imagem de perfil');
        return res.status(404).json({ message: 'Pessoa não possui imagem de perfil' });
      }
      console.log('URL da imagem de perfil:', person.profileImageUrl);

      // Caminho completo da imagem
      const imagePath = path.join(UPLOAD_PATH, person.profileImageUrl);
      console.log('Caminho completo da imagem:', imagePath);

      // Verificar se o arquivo existe
      try {
        console.log('Verificando se o arquivo existe...');
        await fs.access(imagePath);
        console.log('Arquivo encontrado');
      } catch (error) {
        console.log('Arquivo de imagem não encontrado:', error);
        return res.status(404).json({ message: 'Arquivo de imagem não encontrado' });
      }

      // Enviar o arquivo
      console.log('Enviando arquivo...');
      res.sendFile(imagePath);
    } catch (error) {
      console.error('Erro ao buscar imagem de perfil:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Create a new person
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        fullName,
        cpf,
        birthDate,
        address,
        neighborhood,
        city,
        state,
        postalCode,
        phone,
        email,
        gender,
        notes,
        clientId,
        relationship,
        useClientEmail,
        useClientPhone,
      } = req.body;

      // Check if CPF already exists if provided
      if (cpf) {
        const existingPerson = await prisma.person.findUnique({
          where: { cpf },
        });

        if (existingPerson) {
          return res.status(400).json({ message: 'CPF já cadastrado' });
        }
      }

      // Create the person
      const person = await prisma.person.create({
        data: {
          fullName,
          cpf,
          birthDate: birthDate ? new Date(birthDate) : null,
          address,
          neighborhood,
          city,
          state,
          postalCode,
          phone,
          email,
          gender,
          notes,
          clientId,
          relationship,
          useClientEmail: useClientEmail || false,
          useClientPhone: useClientPhone || false,
          createdById: req.user?.id || 'system',
          active: true,
        },
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user?.id || 'system',
          action: 'CREATE',
          entityType: 'Person',
          entityId: person.id,
          details: { requestData: req.body },
          ipAddress: req.ip || req.connection?.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: req.user?.companyId
        }
      });

      res.status(201).json(person);
    } catch (error) {
      console.error('Erro ao criar pessoa:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Get a list of people with filters and pagination
   */
  static async list(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        active,
        clientId,
        relationship,
        companyId,
        personIds
      } = req.query;

      // Processar personIds se existir
      let personIdsArray = [];
      if (personIds) {
        // Se for um único valor, converter para array
        if (!Array.isArray(personIds)) {
          personIdsArray = [personIds];
        } else {
          personIdsArray = personIds;
        }
        console.log("Filtrando por IDs de pacientes:", personIdsArray);
      }

      const where = {
        AND: [
          search
            ? {
                OR: [
                  { fullName: { contains: search, mode: 'insensitive' } },
                  { cpf: { contains: search } },
                  { email: { contains: search, mode: 'insensitive' } },
                  { phone: { contains: search } },
                ],
              }
            : {},
          // Filtrar por IDs específicos se fornecidos
          personIdsArray.length > 0
            ? { id: { in: personIdsArray } }
            : {},
          active !== undefined ? { active: active === 'true' } : {},

          // If user is a client, only show their own persons
          req.user?.isClient
            ? { clientId: req.user.id }
            : clientId ? { clientId } : {},

          // Filtro de relacionamento
          // Se for "Dependente", busca todos que têm clientId e não são "Titular"
          // Se for outro valor (ex: "Titular"), busca exatamente esse valor
          relationship ? (
            relationship === "Dependente"
              ? {
                  clientId: { not: null },
                  relationship: { not: "Titular" }
                }
              : { relationship }
          ) : {},

          // Filtrar por empresa se o parâmetro for fornecido e o usuário for SYSTEM_ADMIN
          companyId && req.user?.role === 'SYSTEM_ADMIN'
            ? {
                client: {
                  createdBy: {
                    companyId: companyId
                  }
                }
              }
            : {},

          // Se não for SYSTEM_ADMIN e não for cliente, limitar à empresa do usuário
          !req.user?.isClient && req.user?.role !== 'SYSTEM_ADMIN' && req.user?.companyId
            ? {
                client: {
                  createdBy: {
                    companyId: req.user?.companyId
                  }
                }
              }
            : {},
        ],
      };

      const [people, total] = await Promise.all([
        prisma.person.findMany({
          where,
          skip: (Number(page) - 1) * Number(limit),
          take: Number(limit),
          orderBy: { fullName: 'asc' },
          include: {
            client: {
              select: {
                id: true,
                email: true,
                login: true,
              },
            },
            personInsurances: {
              include: {
                insurance: true,
              },
              orderBy: {
                insurance: {
                  name: 'asc',
                },
              },
            },
            _count: {
              select: {
                schedulings: true,
                documents: true,
              },
            },
          },
        }),
        prisma.person.count({ where }),
      ]);

      // Adicionar URL completa da imagem de perfil para cada pessoa
      const baseUrl = `${req.protocol}://${req.get('host')}`;
      const peopleWithImageUrls = people.map(person => {
        if (person.profileImageUrl) {
          return {
            ...person,
            profileImageFullUrl: `${baseUrl}/uploads/${person.profileImageUrl}`
          };
        }
        return person;
      });

      // Usar o utilitário para formatar a resposta de forma consistente
      const formattedResponse = formatSuccessResponse(
        peopleWithImageUrls,
        'persons', // Nome padronizado da entidade
        total,
        Math.ceil(total / Number(limit))
      );

      res.json(formattedResponse);
    } catch (error) {
      console.error('Erro ao listar pessoas:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Get a single person by ID
   */
  static async get(req, res) {
    try {
      const { id } = req.params;

      // Build query
      const query = {
        where: { id },
        include: {
          client: {
            select: {
              id: true,
              email: true,
              login: true,
            },
          },
          personInsurances: {
            include: {
              insurance: true,
            },
          },
          documents: {
            select: {
              id: true,
              filename: true,
              type: true,
              createdAt: true,
            },
          },
        },
      };

      const person = await prisma.person.findUnique(query);

      if (!person) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // If user is a client, verify they can access this person
      if (req.user?.isClient && person.clientId !== req.user.id) {
        return res.status(403).json({ message: 'Você não tem permissão para acessar esta pessoa' });
      }

      // Adicionar URL completa da imagem de perfil, se existir
      if (person.profileImageUrl) {
        // Obter a URL base da API a partir do request
        const baseUrl = `${req.protocol}://${req.get('host')}`;
        person.profileImageFullUrl = `${baseUrl}/uploads/${person.profileImageUrl}`;
      }

      // Check if user has access to this person
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) {
        // For company users, check if the person belongs to this company
        if (person.client) {
          const client = await prisma.client.findUnique({
            where: { id: person.client.id },
            select: {
              createdBy: {
                select: {
                  companyId: true,
                },
              },
            },
          });

          if (client?.createdBy?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a esta pessoa' });
          }
        } else {
          // Check if the person was created by a user from this company
          const creator = await prisma.user.findUnique({
            where: { id: person.createdById },
            select: { companyId: true },
          });

          if (creator?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a esta pessoa' });
          }
        }
      }

      res.json(person);
    } catch (error) {
      console.error('Erro ao buscar pessoa:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Update a person
   */
  static async update(req, res) {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      // Check if person exists
      const existingPerson = await prisma.person.findUnique({
        where: { id },
      });

      if (!existingPerson) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // Check access rights
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) {
        // For company users, check if the person belongs to this company
        if (existingPerson.clientId) {
          const client = await prisma.client.findUnique({
            where: { id: existingPerson.clientId },
            select: {
              createdBy: {
                select: {
                  companyId: true,
                },
              },
            },
          });

          if (client?.createdBy?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a esta pessoa' });
          }
        } else {
          // Check if the person was created by a user from this company
          const creator = await prisma.user.findUnique({
            where: { id: existingPerson.createdById },
            select: { companyId: true },
          });

          if (creator?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a esta pessoa' });
          }
        }
      }

      const {
        fullName,
        cpf,
        birthDate,
        address,
        neighborhood,
        city,
        state,
        postalCode,
        phone,
        email,
        gender,
        notes,
        clientId,
        relationship,
        active,
        useClientEmail,
        useClientPhone,
      } = req.body;

      // Check if CPF already exists and is different from current
      if (cpf && cpf !== existingPerson.cpf) {
        const personWithCpf = await prisma.person.findUnique({
          where: { cpf },
        });

        if (personWithCpf) {
          return res.status(400).json({ message: 'CPF já cadastrado' });
        }
      }

      // Update the person
      const person = await prisma.person.update({
        where: { id },
        data: {
          fullName,
          cpf,
          birthDate: birthDate ? new Date(birthDate) : null,
          address,
          neighborhood,
          city,
          state,
          postalCode,
          phone,
          email,
          gender,
          notes,
          clientId,
          relationship,
          useClientEmail: useClientEmail !== undefined ? useClientEmail : existingPerson.useClientEmail,
          useClientPhone: useClientPhone !== undefined ? useClientPhone : existingPerson.useClientPhone,
          active: active !== undefined ? active : existingPerson.active,
        },
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'UPDATE',
          entityType: 'Person',
          entityId: id,
          details: {
            before: existingPerson,
            after: person,
            changes: Object.keys(req.body)
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: req.user.companyId
        }
      });

      res.json(person);
    } catch (error) {
      console.error('Erro ao atualizar pessoa:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Toggle person's active status
   */
  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      // Check if person exists
      const person = await prisma.person.findUnique({
        where: { id },
      });

      if (!person) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // Check access rights
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) {
        // For company users, check if the person belongs to this company
        if (person.clientId) {
          const client = await prisma.client.findUnique({
            where: { id: person.clientId },
            select: {
              createdBy: {
                select: {
                  companyId: true,
                },
              },
            },
          });

          if (client?.createdBy?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a esta pessoa' });
          }
        } else {
          // Check if the person was created by a user from this company
          const creator = await prisma.user.findUnique({
            where: { id: person.createdById },
            select: { companyId: true },
          });

          if (creator?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a esta pessoa' });
          }
        }
      }

      // Toggle active status
      const updatedPerson = await prisma.person.update({
        where: { id },
        data: { active: !person.active },
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'TOGGLE_STATUS',
          entityType: 'Person',
          entityId: id,
          details: {
            previousStatus: person.active,
            newStatus: updatedPerson.active
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: req.user.companyId
        }
      });

      res.json(updatedPerson);
    } catch (error) {
      console.error('Erro ao alterar status da pessoa:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Soft delete a person
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Check if person exists
      const person = await prisma.person.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              schedulings: {
                where: {
                  status: {
                    in: ['PENDING', 'CONFIRMED']
                  }
                }
              }
            }
          }
        }
      });

      if (!person) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // Check access rights
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) {
        // For company users, check if the person belongs to this company
        if (person.clientId) {
          const client = await prisma.client.findUnique({
            where: { id: person.clientId },
            select: {
              createdBy: {
                select: {
                  companyId: true,
                },
              },
            },
          });

          if (client?.createdBy?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a esta pessoa' });
          }
        } else {
          // Check if the person was created by a user from this company
          const creator = await prisma.user.findUnique({
            where: { id: person.createdById },
            select: { companyId: true },
          });

          if (creator?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a esta pessoa' });
          }
        }
      }

      // Check if there are active schedulings
      if (person._count.schedulings > 0) {
        return res.status(400).json({
          message: 'Não é possível excluir uma pessoa com agendamentos pendentes ou confirmados'
        });
      }

      // Iniciar uma transação para garantir que todos os registros relacionados sejam atualizados
      await prisma.$transaction(async (tx) => {
        // Primeiro, excluir os relacionamentos que podem causar problemas
        // Excluir convênios relacionados à pessoa
        await tx.personInsurance.deleteMany({
          where: { personId: id }
        });

        // Excluir contatos relacionados à pessoa
        await tx.contact.deleteMany({
          where: { personId: id }
        });

        // Soft delete da pessoa
        await tx.person.update({
          where: { id },
          data: {
            active: false,
            deletedAt: new Date(),
            deletedById: req.user.id
          }
        });
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'DELETE',
          entityType: 'Person',
          entityId: id,
          details: {
            softDelete: true,
            personData: {
              fullName: person.fullName,
              cpf: person.cpf,
              email: person.email
            }
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: req.user.companyId
        }
      });

      res.status(204).send();
    } catch (error) {
      console.error('Erro ao excluir pessoa:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Add insurance to a person
   */
  static async addInsurance(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { personId, insuranceId, policyNumber, validUntil, notes } = req.body;

      // Check if person exists
      const person = await prisma.person.findUnique({
        where: { id: personId },
      });

      if (!person) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // Check if insurance exists
      const insurance = await prisma.insurance.findUnique({
        where: { id: insuranceId },
      });

      if (!insurance) {
        return res.status(404).json({ message: 'Convênio não encontrado' });
      }

      // Check if association already exists
      const existingAssociation = await prisma.personInsurance.findUnique({
        where: {
          personId_insuranceId: {
            personId,
            insuranceId,
          },
        },
      });

      if (existingAssociation) {
        return res.status(400).json({ message: 'Pessoa já possui este convênio' });
      }

      // Create association
      const personInsurance = await prisma.personInsurance.create({
        data: {
          personId,
          insuranceId,
          policyNumber,
          validUntil: validUntil ? new Date(validUntil) : null,
          notes,
        },
        include: {
          person: {
            select: {
              id: true,
              fullName: true,
            },
          },
          insurance: true,
        },
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'ADD_INSURANCE',
          entityType: 'Person',
          entityId: personId,
          details: {
            insuranceId,
            insuranceName: insurance.name,
            policyNumber,
            validUntil
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: req.user.companyId
        }
      });

      res.status(201).json(personInsurance);
    } catch (error) {
      console.error('Erro ao adicionar convênio:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Remove insurance from a person
   */
  static async removeInsurance(req, res) {
    try {
      const { personId, insuranceId } = req.params;

      // Check if association exists
      const personInsurance = await prisma.personInsurance.findUnique({
        where: {
          personId_insuranceId: {
            personId,
            insuranceId,
          },
        },
        include: {
          insurance: true,
        },
      });

      if (!personInsurance) {
        return res.status(404).json({ message: 'Associação não encontrada' });
      }

      // Delete association
      await prisma.personInsurance.delete({
        where: {
          personId_insuranceId: {
            personId,
            insuranceId,
          },
        },
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'REMOVE_INSURANCE',
          entityType: 'Person',
          entityId: personId,
          details: {
            insuranceId,
            insuranceName: personInsurance.insurance.name
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: req.user.companyId
        }
      });

      res.status(204).send();
    } catch (error) {
      console.error('Erro ao remover convênio:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Update insurance details for a person
   */
  static async updateInsurance(req, res) {
    try {
      const { personId, insuranceId } = req.params;
      const { policyNumber, validUntil, notes } = req.body;

      // Check if association exists
      const personInsurance = await prisma.personInsurance.findUnique({
        where: {
          personId_insuranceId: {
            personId,
            insuranceId,
          },
        },
      });

      if (!personInsurance) {
        return res.status(404).json({ message: 'Associação não encontrada' });
      }

      // Update association
      const updatedPersonInsurance = await prisma.personInsurance.update({
        where: {
          personId_insuranceId: {
            personId,
            insuranceId,
          },
        },
        data: {
          policyNumber,
          validUntil: validUntil ? new Date(validUntil) : null,
          notes,
        },
        include: {
          person: {
            select: {
              id: true,
              fullName: true,
            },
          },
          insurance: true,
        },
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'UPDATE_INSURANCE',
          entityType: 'Person',
          entityId: personId,
          details: {
            insuranceId,
            insuranceName: updatedPersonInsurance.insurance.name,
            changes: {
              policyNumber: policyNumber !== personInsurance.policyNumber,
              validUntil: validUntil !== personInsurance.validUntil,
              notes: notes !== personInsurance.notes
            }
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: req.user.companyId
        }
      });

      res.json(updatedPersonInsurance);
    } catch (error) {
      console.error('Erro ao atualizar dados do convênio:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Get profile image for a person
   */
  static async getProfileImage(req, res) {
    try {
      const { id } = req.params;

      // Verificar se a pessoa existe
      const person = await prisma.person.findUnique({
        where: { id },
        select: {
          id: true,
          profileImageUrl: true
        }
      });

      if (!person) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // Verificar se a pessoa tem uma imagem de perfil
      if (!person.profileImageUrl) {
        return res.status(404).json({ message: 'Imagem de perfil não encontrada' });
      }

      // Construir o caminho completo para a imagem
      const imagePath = path.join(UPLOAD_PATH, person.profileImageUrl);
      console.log('Caminho completo da imagem:', imagePath);

      // Verificar se o arquivo existe
      if (!fs.existsSync(imagePath)) {
        console.error('Arquivo não encontrado:', imagePath);
        return res.status(404).json({ message: 'Arquivo de imagem não encontrado' });
      }

      // Obter o tipo MIME com base na extensão do arquivo
      const ext = path.extname(imagePath).toLowerCase();
      let contentType = 'image/jpeg'; // Padrão

      if (ext === '.png') contentType = 'image/png';
      else if (ext === '.gif') contentType = 'image/gif';
      else if (ext === '.webp') contentType = 'image/webp';

      // Enviar o arquivo
      res.setHeader('Content-Type', contentType);
      res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache por 24 horas

      // Criar um stream de leitura e enviar o arquivo
      const fileStream = fs.createReadStream(imagePath);
      fileStream.pipe(res);
    } catch (error) {
      console.error('Erro ao obter imagem de perfil:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * List all insurances for a person
   */
  static async listInsurances(req, res) {
    try {
      const { personId } = req.params;

      // Check if person exists
      const person = await prisma.person.findUnique({
        where: { id: personId },
      });

      if (!person) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // Get all insurances
      const personInsurances = await prisma.personInsurance.findMany({
        where: { personId },
        include: {
          insurance: true,
        },
        orderBy: {
          insurance: {
            name: 'asc',
          },
        },
      });

      res.json(personInsurances);
    } catch (error) {
      console.error('Erro ao listar convênios da pessoa:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
}

module.exports = {
  PersonController,
  createPersonValidation,
};