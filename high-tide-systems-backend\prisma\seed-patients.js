// prisma/seed-patients.js
const { PrismaClient } = require('@prisma/client');
const axios = require('axios');
const prisma = new PrismaClient();

// Lista de CEPs reais de diferentes cidades brasileiras
const realCEPs = [
  '01310200', // São Paulo - SP (Av. Paulista)
  '22031001', // Rio de Janeiro - RJ (Copacabana)
  '30130110', // Belo Horizonte - MG (Centro)
  '40010020', // Salvador - BA (Centro)
  '50030230', // Recife - PE (Boa Vista)
  '90010280', // Porto Alegre - RS (Centro)
  '80010010', // Curitiba - PR (Centro)
  '60060170', // Fortaleza - CE (Aldeota)
  '70070120', // Brasília - DF (Asa Sul)
  '69010060', // Manaus - AM (Centro)
  '29055340', // Vitória - ES (Praia do Canto)
  '66010020', // Belém - PA (Campina)
  '59020030', // Natal - RN (Petrópolis)
  '58010000', // <PERSON> (Centro)
  '49010040', // Aracaju - SE (Centro)
  '64000290', // Teresina - PI (Centro)
  '78005370', // Cuiabá - MT (Centro)
  '79002170', // Campo Grande - MS (Centro)
  '74030010', // Goiânia - GO (Setor Central)
  '69900100'  // Rio Branco - AC (Centro)
];

// Função para gerar um nome aleatório
function getRandomName() {
  const firstNames = [
    'Ana', 'João', 'Maria', 'Pedro', 'Carla', 'Lucas', 'Juliana', 'Marcos', 'Fernanda', 'Rafael',
    'Patrícia', 'Bruno', 'Camila', 'Gustavo', 'Mariana', 'Ricardo', 'Aline', 'Thiago', 'Daniela', 'Felipe',
    'Luciana', 'Eduardo', 'Beatriz', 'Rodrigo', 'Amanda', 'Carlos', 'Larissa', 'Paulo', 'Natália', 'Marcelo',
    'Sofia', 'Miguel', 'Helena', 'Arthur', 'Alice', 'Davi', 'Laura', 'Bernardo', 'Valentina', 'Heitor'
  ];
  
  const lastNames = [
    'Silva', 'Santos', 'Oliveira', 'Souza', 'Pereira', 'Costa', 'Rodrigues', 'Almeida', 'Nascimento', 'Lima',
    'Araújo', 'Fernandes', 'Carvalho', 'Gomes', 'Martins', 'Rocha', 'Ribeiro', 'Alves', 'Monteiro', 'Mendes',
    'Barros', 'Freitas', 'Barbosa', 'Pinto', 'Moura', 'Cavalcanti', 'Dias', 'Castro', 'Campos', 'Cardoso'
  ];
  
  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
  const lastName1 = lastNames[Math.floor(Math.random() * lastNames.length)];
  const lastName2 = lastNames[Math.floor(Math.random() * lastNames.length)];
  
  return `${firstName} ${lastName1} ${lastName2}`;
}

// Função para gerar um CPF válido
function generateCPF() {
  const n1 = Math.floor(Math.random() * 10);
  const n2 = Math.floor(Math.random() * 10);
  const n3 = Math.floor(Math.random() * 10);
  const n4 = Math.floor(Math.random() * 10);
  const n5 = Math.floor(Math.random() * 10);
  const n6 = Math.floor(Math.random() * 10);
  const n7 = Math.floor(Math.random() * 10);
  const n8 = Math.floor(Math.random() * 10);
  const n9 = Math.floor(Math.random() * 10);
  
  let d1 = n9 * 2 + n8 * 3 + n7 * 4 + n6 * 5 + n5 * 6 + n4 * 7 + n3 * 8 + n2 * 9 + n1 * 10;
  d1 = 11 - (d1 % 11);
  if (d1 >= 10) d1 = 0;
  
  let d2 = d1 * 2 + n9 * 3 + n8 * 4 + n7 * 5 + n6 * 6 + n5 * 7 + n4 * 8 + n3 * 9 + n2 * 10 + n1 * 11;
  d2 = 11 - (d2 % 11);
  if (d2 >= 10) d2 = 0;
  
  return `${n1}${n2}${n3}${n4}${n5}${n6}${n7}${n8}${n9}${d1}${d2}`;
}

// Função para gerar um email a partir do nome
function generateEmail(name) {
  const normalizedName = name
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/\s+/g, '.');
  
  const domains = ['gmail.com', 'hotmail.com', 'outlook.com', 'yahoo.com.br', 'uol.com.br'];
  const domain = domains[Math.floor(Math.random() * domains.length)];
  
  return `${normalizedName}@${domain}`;
}

// Função para gerar um número de telefone
function generatePhone() {
  const ddd = Math.floor(Math.random() * 89) + 11; // DDD entre 11 e 99
  const part1 = Math.floor(Math.random() * 9000) + 1000;
  const part2 = Math.floor(Math.random() * 9000) + 1000;
  
  return `(${ddd}) 9${part1}-${part2}`;
}

// Função para gerar uma data de nascimento (entre 0 e 80 anos)
function generateBirthDate(minAge = 0, maxAge = 80) {
  const now = new Date();
  
  const minYear = now.getFullYear() - maxAge;
  const maxYear = now.getFullYear() - minAge;
  
  const year = Math.floor(Math.random() * (maxYear - minYear + 1)) + minYear;
  const month = Math.floor(Math.random() * 12);
  const day = Math.floor(Math.random() * 28) + 1; // Evita problemas com meses com menos de 31 dias
  
  return new Date(year, month, day);
}

// Função para buscar endereço a partir do CEP usando a API ViaCEP
async function getAddressByCEP(cep) {
  try {
    const response = await axios.get(`https://viacep.com.br/ws/${cep}/json/`);
    
    if (response.data.erro) {
      throw new Error('CEP não encontrado');
    }
    
    return {
      cep: response.data.cep,
      logradouro: response.data.logradouro,
      complemento: response.data.complemento,
      bairro: response.data.bairro,
      localidade: response.data.localidade,
      uf: response.data.uf
    };
  } catch (error) {
    console.error(`Erro ao buscar CEP ${cep}:`, error.message);
    // Retorna um endereço padrão em caso de erro
    return {
      cep: cep,
      logradouro: 'Rua Exemplo',
      complemento: '',
      bairro: 'Centro',
      localidade: 'São Paulo',
      uf: 'SP'
    };
  }
}

// Função para gerar um número aleatório entre min e max (inclusive)
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Função para gerar um relacionamento aleatório
function getRandomRelationship() {
  const relationships = [
    'Cônjuge', 'Filho(a)', 'Pai', 'Mãe', 'Irmão(ã)', 'Avô(ó)', 'Neto(a)', 'Tio(a)', 'Sobrinho(a)', 'Primo(a)'
  ];
  
  return relationships[Math.floor(Math.random() * relationships.length)];
}

// Função principal
async function main() {
  console.log('Iniciando seed de pacientes...');
  
  try {
    // Buscar todos os clientes com suas pessoas titulares e convênios
    const clients = await prisma.client.findMany({
      where: {
        active: true
      },
      include: {
        persons: {
          where: {
            relationship: 'Titular'
          }
        },
        ClientInsurance: {
          include: {
            Insurance: true
          }
        },
        createdBy: true
      }
    });
    
    console.log(`Encontrados ${clients.length} clientes para adicionar pacientes`);
    
    // Para cada cliente, criar entre 0 e 4 pacientes adicionais
    for (const client of clients) {
      console.log(`\nProcessando cliente: ${client.email} (ID: ${client.id})`);
      
      // Verificar se o cliente tem uma pessoa titular
      if (!client.persons || client.persons.length === 0) {
        console.log(`Cliente ${client.email} não tem pessoa titular. Pulando...`);
        continue;
      }
      
      const titular = client.persons[0];
      console.log(`Pessoa titular: ${titular.fullName}`);
      
      // Verificar se o cliente tem convênios
      if (!client.ClientInsurance || client.ClientInsurance.length === 0) {
        console.log(`Cliente ${client.email} não tem convênios. Pacientes serão criados sem convênios.`);
      } else {
        console.log(`Cliente tem ${client.ClientInsurance.length} convênios.`);
      }
      
      // Determinar quantos pacientes criar (entre 0 e 4, com maior probabilidade para 1)
      const numPatients = (() => {
        const rand = Math.random();
        if (rand < 0.15) return 0;      // 15% de chance de 0 pacientes
        if (rand < 0.65) return 1;      // 50% de chance de 1 paciente
        if (rand < 0.85) return 2;      // 20% de chance de 2 pacientes
        if (rand < 0.95) return 3;      // 10% de chance de 3 pacientes
        return 4;                       // 5% de chance de 4 pacientes
      })();
      
      console.log(`Criando ${numPatients} pacientes adicionais para o cliente ${client.email}`);
      
      // Criar os pacientes
      for (let i = 0; i < numPatients; i++) {
        try {
          // Gerar dados do paciente
          const fullName = getRandomName();
          const relationship = getRandomRelationship();
          const cpf = generateCPF();
          const email = Math.random() > 0.5 ? generateEmail(fullName) : null; // 50% de chance de ter email
          const phone = Math.random() > 0.7 ? generatePhone() : null; // 30% de chance de ter telefone
          
          // Gerar data de nascimento com base no relacionamento
          let birthDate;
          if (relationship === 'Filho(a)' || relationship === 'Sobrinho(a)' || relationship === 'Neto(a)') {
            // Pessoas mais jovens (0-25 anos)
            birthDate = generateBirthDate(0, 25);
          } else if (relationship === 'Pai' || relationship === 'Mãe' || relationship === 'Avô(ó)' || relationship === 'Tio(a)') {
            // Pessoas mais velhas (40-80 anos)
            birthDate = generateBirthDate(40, 80);
          } else {
            // Outras relações (18-70 anos)
            birthDate = generateBirthDate(18, 70);
          }
          
          // Decidir se usa o mesmo endereço do titular (70% de chance)
          let address, neighborhood, city, state, postalCode;
          
          if (Math.random() < 0.7) {
            // Usar o mesmo endereço do titular
            address = titular.address;
            neighborhood = titular.neighborhood;
            city = titular.city;
            state = titular.state;
            postalCode = titular.postalCode;
          } else {
            // Obter um CEP aleatório da lista
            const randomCEP = realCEPs[Math.floor(Math.random() * realCEPs.length)];
            
            // Buscar endereço pelo CEP
            const addressData = await getAddressByCEP(randomCEP);
            
            address = addressData.logradouro;
            neighborhood = addressData.bairro;
            city = addressData.localidade;
            state = addressData.uf;
            postalCode = addressData.cep.replace('-', '');
          }
          
          // Verificar se o CPF já existe
          const existingPerson = await prisma.person.findUnique({
            where: { cpf }
          });
          
          if (existingPerson) {
            console.log(`Pessoa com CPF ${cpf} já existe. Pulando...`);
            continue;
          }
          
          // Criar a pessoa
          const person = await prisma.person.create({
            data: {
              fullName,
              cpf,
              birthDate,
              address,
              neighborhood,
              city,
              state,
              postalCode,
              phone,
              email,
              gender: Math.random() > 0.5 ? 'M' : 'F',
              notes: `Paciente gerado automaticamente, ${relationship} de ${titular.fullName}`,
              relationship,
              clientId: client.id,
              createdById: client.createdBy.id
            }
          });
          
          console.log(`✅ Paciente criado: ${person.fullName} (${relationship} de ${titular.fullName})`);
          
          // Se o cliente tem convênios, associar o mesmo convênio ao paciente (80% de chance)
          if (client.ClientInsurance && client.ClientInsurance.length > 0 && Math.random() < 0.8) {
            // Selecionar aleatoriamente um dos convênios do cliente
            const randomInsurance = client.ClientInsurance[Math.floor(Math.random() * client.ClientInsurance.length)];
            
            // Associar o convênio ao paciente
            await prisma.personInsurance.create({
              data: {
                personId: person.id,
                insuranceId: randomInsurance.insuranceId,
                policyNumber: randomInsurance.policyNumber || `${Math.floor(Math.random() * 1000000)}`,
                validUntil: randomInsurance.validUntil || new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
                notes: `Convênio associado automaticamente ao paciente ${fullName}`
              }
            });
            
            console.log(`✅ Convênio ${randomInsurance.Insurance.name} associado ao paciente ${person.fullName}`);
          }
        } catch (error) {
          console.error(`Erro ao criar paciente para cliente ${client.email}:`, error);
        }
      }
    }
    
    console.log('\nSeed de pacientes concluído com sucesso!');
  } catch (error) {
    console.error('Erro durante o seed de pacientes:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Erro durante o seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
