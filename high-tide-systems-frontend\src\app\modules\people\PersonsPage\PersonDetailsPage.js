"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  ArrowLeft,
  Edit,
  Plus,
  Trash,
  Power,
  User,
  Mail,
  Phone,
  Calendar,
  CreditCard,
  MapPin,
  FileText,
  CheckCircle,
  XCircle,
  Upload,
  Loader2,
  Download,
  Eye,
  Users,
  RefreshCw,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { personsService } from "@/app/modules/people/services/personsService";
import PersonFormModal from "@/components/people/PersonFormModal";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { api } from "@/utils/api";
import PersonDocumentsSection from "@/components/people/PersonDocumentsSection";
import PersonContactsSection from "@/components/people/PersonContactsSection";
import PersonInsuranceLimitsTab from '@/components/people/PersonInsuranceLimitsTab';
import PersonInsurancesTab from '@/components/people/PersonInsurancesTab';

const PersonDetailsPage = ({ personId }) => {
  const router = useRouter();
  const fileInputRef = useRef(null);
  const [person, setPerson] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [client, setClient] = useState(null);
  const [documents, setDocuments] = useState([]);
  const [isLoadingDocuments, setIsLoadingDocuments] = useState(false);
  const [activeTab, setActiveTab] = useState("documents"); // "documents", "contacts", "insurances" ou "insuranceLimits"

  // Modals and dialogs state
  const [personFormOpen, setPersonFormOpen] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState(null);
  const [documentUploadOpen, setDocumentUploadOpen] = useState(false);
  const [documentType, setDocumentType] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState(null);

  useEffect(() => {
    loadPersonData();
  }, [personId]);

  const loadPersonData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Carregando dados da pessoa:', personId);
      const data = await personsService.getPerson(personId);
      console.log('Dados da pessoa carregados:', data);
      console.log('URL da imagem de perfil:', data.profileImageFullUrl);
      setPerson(data);

      // If person has a client, get client details
      if (data.clientId) {
        setClient(data.client);
      }

      // Load documents
      loadDocuments();
    } catch (err) {
      console.error("Error fetching person details:", err);
      setError("Não foi possível carregar os dados da pessoa. Por favor, tente novamente.");
    } finally {
      setIsLoading(false);
    }
  };

  const loadDocuments = async () => {
    setIsLoadingDocuments(true);
    try {
      const response = await api.get("/documents", {
        params: {
          targetId: personId,
          targetType: "person"
        }
      });
      setDocuments(response.data || []);
    } catch (err) {
      console.error("Error fetching documents:", err);
    } finally {
      setIsLoadingDocuments(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";

    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return "Data inválida";
    }
  };

  const formatCPF = (cpf) => {
    if (!cpf) return "N/A";

    // CPF format: 000.000.000-00
    const cpfNumbers = cpf.replace(/\D/g, '');
    return cpfNumbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  };

  const formatPhone = (phone) => {
    if (!phone) return "N/A";

    // Phone format: (00) 00000-0000
    const phoneNumbers = phone.replace(/\D/g, '');
    return phoneNumbers.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  };

  const handleEditPerson = async () => {
    console.log('Abrindo modal de edição da pessoa');
    console.log('Dados da pessoa antes de abrir o modal:', person);
    console.log('URL da imagem de perfil antes de abrir o modal:', person.profileImageFullUrl);

    // Buscar dados atualizados da pessoa antes de abrir o modal
    try {
      const updatedPerson = await personsService.getPerson(personId);
      console.log('Dados atualizados da pessoa:', updatedPerson);
      console.log('URL da imagem de perfil atualizada:', updatedPerson.profileImageFullUrl);

      // Atualizar os dados da pessoa no estado
      setPerson(updatedPerson);
    } catch (error) {
      console.error('Erro ao buscar dados atualizados da pessoa:', error);
    }

    setPersonFormOpen(true);
  };

  const handleTogglePersonStatus = () => {
    setConfirmAction({
      type: "toggle-person-status",
      title: `${person.active ? "Desativar" : "Ativar"} Pessoa`,
      message: `Deseja ${person.active ? "desativar" : "ativar"} ${person.fullName}?`,
    });
    setConfirmDialogOpen(true);
  };

  const handleDeletePerson = () => {
    setConfirmAction({
      type: "delete-person",
      title: "Excluir Pessoa",
      message: `Deseja excluir permanentemente ${person.fullName}?`,
      variant: "danger",
    });
    setConfirmDialogOpen(true);
  };

  const handleOpenDocumentUpload = (type) => {
    setDocumentType(type);
    setSelectedFile(null);
    setUploadError(null);
    setDocumentUploadOpen(true);
  };

  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
      setUploadError(null);
    }
  };

  const handleUploadDocument = async () => {
    if (!selectedFile || !documentType) {
      setUploadError("Selecione um arquivo e um tipo de documento");
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setUploadError(null);

    const formData = new FormData();
    formData.append("documents", selectedFile);
    formData.append("types", JSON.stringify([documentType]));

    try {
      await api.post(`/documents/upload?targetId=${personId}&targetType=person`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        },
      });

      // Reload documents after successful upload
      loadDocuments();
      setDocumentUploadOpen(false);
    } catch (err) {
      console.error("Error uploading document:", err);
      setUploadError(err.response?.data?.message || "Erro ao fazer upload do documento");
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteDocument = (documentId) => {
    setConfirmAction({
      type: "delete-document",
      title: "Excluir Documento",
      message: "Deseja excluir permanentemente este documento?",
      variant: "danger",
      documentId,
    });
    setConfirmDialogOpen(true);
  };

  const handleViewDocument = (documentId) => {
    window.open(`/api/documents/${documentId}`, '_blank');
  };

  const confirmActionHandler = async () => {
    try {
      if (confirmAction.type === "toggle-person-status") {
        await personsService.togglePersonStatus(personId);
        loadPersonData();
      } else if (confirmAction.type === "delete-person") {
        await personsService.deletePerson(personId);
        router.push("/dashboard/people/persons");
        return; // No need to reload data
      } else if (confirmAction.type === "delete-document") {
        await api.delete(`/documents/${confirmAction.documentId}`);
        loadDocuments();
      }
    } catch (error) {
      console.error("Error executing action:", error);
      setError("Ocorreu um erro ao executar esta ação.");
    } finally {
      setConfirmDialogOpen(false);
    }
  };

  const getGenderDisplay = (gender) => {
    if (!gender) return "Não informado";

    const genderMap = {
      "M": "Masculino",
      "F": "Feminino",
      "O": "Outro"
    };

    return genderMap[gender] || gender;
  };

  const getDocumentTypeDisplay = (type) => {
    const typeMap = {
      "RG": "RG",
      "CPF": "CPF",
      "CNH": "Carteira de Motorista",
      "COMP_RESIDENCIA": "Comprovante de Residência",
      "CERTIDAO_NASCIMENTO": "Certidão de Nascimento",
      "CERTIDAO_CASAMENTO": "Certidão de Casamento",
      "CARTAO_VACINACAO": "Cartão de Vacinação",
      "PASSAPORTE": "Passaporte",
      "TITULO_ELEITOR": "Título de Eleitor",
      "CARTEIRA_TRABALHO": "Carteira de Trabalho",
      "OUTROS": "Outros"
    };

    return typeMap[type] || type;
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[300px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary-500 dark:text-primary-400 mb-4" />
        <p className="text-neutral-600 dark:text-neutral-300">Carregando dados da pessoa...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-300">
        <h3 className="text-lg font-medium mb-2">Erro</h3>
        <p>{error}</p>
        <div className="mt-4">
          <button
            onClick={() => router.push("/dashboard/people/persons")}
            className="px-4 py-2 bg-white dark:bg-gray-800 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2"
          >
            <ArrowLeft size={18} />
            <span>Voltar para lista de pessoas</span>
          </button>
        </div>
      </div>
    );
  }

  if (!person) {
    return (
      <div className="bg-neutral-50 dark:bg-gray-800 p-6 rounded-lg border border-neutral-200 dark:border-gray-700 text-neutral-700 dark:text-neutral-300">
        <h3 className="text-lg font-medium mb-2">Pessoa não encontrada</h3>
        <p>A pessoa solicitada não foi encontrada.</p>
        <div className="mt-4">
          <button
            onClick={() => router.push("/dashboard/people/persons")}
            className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors flex items-center gap-2"
          >
            <ArrowLeft size={18} />
            <span>Voltar para lista de pessoas</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-2">
          <button
            onClick={() => router.push("/dashboard/people/persons")}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
          >
            <ArrowLeft size={18} className="text-neutral-800 dark:text-neutral-200" />
          </button>
          <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">Detalhes da Pessoa</h1>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <button
            onClick={handleEditPerson}
            className="flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
          >
            <Edit size={16} />
            <span>Editar</span>
          </button>

          <button
            onClick={handleTogglePersonStatus}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${person.active
              ? "bg-amber-500 dark:bg-amber-600 text-white hover:bg-amber-600 dark:hover:bg-amber-700"
              : "bg-green-500 dark:bg-green-600 text-white hover:bg-green-600 dark:hover:bg-green-700"
              }`}
          >
            <Power size={16} />
            <span>{person.active ? "Desativar" : "Ativar"}</span>
          </button>

          <button
            onClick={handleDeletePerson}
            className="flex items-center gap-2 px-3 py-2 bg-red-500 dark:bg-red-600 text-white rounded-lg hover:bg-red-600 dark:hover:bg-red-700 transition-colors"
          >
            <Trash size={16} />
            <span>Excluir</span>
          </button>
        </div>
      </div>

      {/* Person Info Card */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm dark:shadow-lg dark:shadow-black/30 p-6 border border-neutral-100 dark:border-gray-700">
        <div className="flex flex-col md:flex-row md:items-start gap-6">
          <div className="flex-shrink-0 flex items-center justify-center w-20 h-20 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full overflow-hidden">
            {person.profileImageFullUrl ? (
              <img
                src={person.profileImageFullUrl}
                alt={person.fullName}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.style.display = 'none';
                  e.target.parentNode.innerHTML = `<div class="flex items-center justify-center w-full h-full"><User size={32} /></div>`;
                }}
              />
            ) : (
              <User size={32} />
            )}
          </div>

          <div className="flex-1 space-y-6 w-full">
            <div>
              <h2 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">{person.fullName}</h2>
              <div className="flex items-center gap-3 mt-1">
                <span className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${person.active
                  ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                  : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                  }`}>
                  {person.active ? (
                    <>
                      <CheckCircle size={12} />
                      <span>Ativo</span>
                    </>
                  ) : (
                    <>
                      <XCircle size={12} />
                      <span>Inativo</span>
                    </>
                  )}
                </span>

                {person.clientId && (
                  <span className="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 flex items-center gap-1">
                    <Users size={12} />
                    <span>{person.relationship || "Titular"}</span>
                  </span>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {person.cpf && (
                <div>
                  <p className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">CPF</p>
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                    <p className="text-neutral-800 dark:text-neutral-200">{formatCPF(person.cpf)}</p>
                  </div>
                </div>
              )}

              {person.birthDate && (
                <div>
                  <p className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Data de Nascimento</p>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                    <p className="text-neutral-800 dark:text-neutral-200">{formatDate(person.birthDate)}</p>
                  </div>
                </div>
              )}

              {person.gender && (
                <div>
                  <p className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Gênero</p>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                    <p className="text-neutral-800 dark:text-neutral-200">{getGenderDisplay(person.gender)}</p>
                  </div>
                </div>
              )}

              {person.email && (
                <div>
                  <p className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Email</p>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                    <p className="text-neutral-800 dark:text-neutral-200">{person.email}</p>
                  </div>
                </div>
              )}

              {person.phone && (
                <div>
                  <p className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Telefone</p>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                    <p className="text-neutral-800 dark:text-neutral-200">{formatPhone(person.phone)}</p>
                  </div>
                </div>
              )}

              {person.clientId && client && (
                <div>
                  <p className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Cliente</p>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                    <button
                      onClick={() => router.push(`/dashboard/people/clients/${person.clientId}`)}
                      className="text-primary-600 dark:text-primary-400 hover:underline"
                    >
                      {client.login || client.email}
                    </button>
                  </div>
                </div>
              )}

              {person.address && (
                <div className="md:col-span-2">
                  <p className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Endereço</p>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                    <p className="text-neutral-800 dark:text-neutral-200">{person.address}</p>
                  </div>
                </div>
              )}
            </div>

            {person.notes && (
              <div>
                <p className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Observações</p>
                <div className="bg-neutral-50 dark:bg-gray-700 p-4 rounded-lg text-neutral-700 dark:text-neutral-300">
                  {person.notes}
                </div>
              </div>
            )}

            {person.createdAt && (
              <div className="pt-4 border-t border-neutral-200 dark:border-gray-700">
                <p className="text-sm text-neutral-500 dark:text-neutral-400">
                  Cadastrado em {formatDate(person.createdAt)}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700">
        <div className="flex border-b border-neutral-200 dark:border-gray-700">
          <button
            onClick={() => setActiveTab("documents")}
            className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "documents"
              ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
              : "text-neutral-600 dark:text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-100 hover:bg-neutral-50 dark:hover:bg-gray-700"
              }`}
          >
            <FileText size={16} />
            <span>Documentos</span>
          </button>
          <button
            onClick={() => setActiveTab("contacts")}
            className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "contacts"
              ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
              : "text-neutral-600 dark:text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-100 hover:bg-neutral-50 dark:hover:bg-gray-700"
              }`}
          >
            <Users size={16} />
            <span>Contatos</span>
          </button>
          <button
            onClick={() => setActiveTab("insurances")}
            className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "insurances"
              ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
              : "text-neutral-600 dark:text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-100 hover:bg-neutral-50 dark:hover:bg-gray-700"
              }`}
          >
            <CreditCard size={16} />
            <span>Convênios</span>
          </button>
          <button
            onClick={() => setActiveTab("insuranceLimits")}
            className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "insuranceLimits"
              ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
              : "text-neutral-600 dark:text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-100 hover:bg-neutral-50 dark:hover:bg-gray-700"
              }`}
          >
            <CreditCard size={16} />
            <span>Limites de Convênio</span>
          </button>
        </div>

        <div className="p-6">
          {activeTab === "documents" ? (
            <PersonDocumentsSection personId={personId} />
          ) : activeTab === "contacts" ? (
            <PersonContactsSection personId={personId} />
          ) : activeTab === "insurances" ? (
            <PersonInsurancesTab personId={personId} />
          ) : (
            <PersonInsuranceLimitsTab personId={personId} />
          )}
        </div>
      </div>

      {/* Document Upload Modal */}
      {documentType && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center overflow-y-auto ${documentUploadOpen ? 'block' : 'hidden'}`}>
          <div className="fixed inset-0 bg-black/50" onClick={() => setDocumentUploadOpen(false)}></div>

          <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-md w-full z-[55]">
            <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
              <h3 className="text-xl font-semibold text-neutral-800 dark:text-neutral-100">
                Upload de {getDocumentTypeDisplay(documentType)}
              </h3>
              <button
                onClick={() => setDocumentUploadOpen(false)}
                className="text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200"
              >
                <Trash size={20} />
              </button>
            </div>

            <div className="p-6">
              <div className="space-y-4">
                <div className="border-2 border-dashed border-neutral-300 dark:border-gray-600 rounded-lg p-4 text-center">
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    onChange={handleFileChange}
                    accept=".pdf,.jpg,.jpeg,.png"
                  />
                  {selectedFile ? (
                    <div className="space-y-2">
                      <div className="flex items-center justify-center gap-2 text-primary-600 dark:text-primary-400">
                        <FileText size={24} />
                        <span className="font-medium">{selectedFile.name}</span>
                      </div>
                      <p className="text-neutral-500 dark:text-neutral-400 text-sm">{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                      <button
                        onClick={() => setSelectedFile(null)}
                        className="px-3 py-1 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300 rounded-lg text-sm hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors"
                      >
                        Alterar arquivo
                      </button>
                    </div>
                  ) : (
                    <div onClick={() => fileInputRef.current.click()} className="cursor-pointer py-6">
                      <Upload className="h-10 w-10 mx-auto text-neutral-400 dark:text-neutral-500 mb-2" />
                      <p className="text-neutral-600 dark:text-neutral-300 mb-1">Clique para selecionar um arquivo</p>
                      <p className="text-neutral-500 dark:text-neutral-400 text-sm">PDF, JPG ou PNG (máx. 10MB)</p>
                    </div>
                  )}
                </div>

                {uploadError && (
                  <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400 rounded-lg text-sm">
                    {uploadError}
                  </div>
                )}

                {isUploading && (
                  <div className="space-y-2">
                    <div className="h-2 w-full bg-neutral-200 dark:bg-gray-700 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-primary-500 dark:bg-primary-600 rounded-full"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                    <p className="text-center text-neutral-600 dark:text-neutral-300 text-sm">
                      {uploadProgress}% concluído
                    </p>
                  </div>
                )}

                <div className="flex justify-end gap-3 pt-2">
                  <button
                    onClick={() => setDocumentUploadOpen(false)}
                    className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
                    disabled={isUploading}
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleUploadDocument}
                    className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2"
                    disabled={!selectedFile || isUploading}
                  >
                    {isUploading ? (
                      <>
                        <Loader2 size={16} className="animate-spin" />
                        <span>Enviando...</span>
                      </>
                    ) : (
                      <>
                        <Upload size={16} />
                        <span>Enviar</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Person Form Modal */}
      {personFormOpen && (
        <PersonFormModal
          isOpen={personFormOpen}
          onClose={() => setPersonFormOpen(false)}
          person={person}
          onSuccess={() => {
            setPersonFormOpen(false);
            loadPersonData();
          }}
        />
      )}

      {/* Confirmation Dialog */}
      {confirmDialogOpen && confirmAction && (
        <ConfirmationDialog
          isOpen={confirmDialogOpen}
          onClose={() => setConfirmDialogOpen(false)}
          onConfirm={confirmActionHandler}
          title={confirmAction.title}
          message={confirmAction.message}
          variant={confirmAction.variant || "warning"}
          confirmText={confirmAction.confirmText || "Confirmar"}
          cancelText={confirmAction.cancelText || "Cancelar"}
        />
      )}
    </div>
  );
};

export default PersonDetailsPage;